"""
趋势线策略分析配置文件
对应Oracle gp_Trend_pkg的配置参数
"""

# 趋势分析配置参数
TREND_CONFIG = {
    # 时间框架配置 - 对应Oracle dates_type参数
    'time_frames': [200, 400],  # Oracle: Trend_<PERSON>ly(200), Trend_<PERSON>ly(400)
    
    # 趋势线验证参数
    'trend_validation': {
        'check_days': 3,           # Oracle: between p_num + 1 and p_num + 3
        'price_tolerance': 1.03,   # Oracle: p_price * 1.03
    },
    
    # 平均交易量计算参数
    'avg_volume': {
        'calculation_days': 30,    # Oracle: CALCUL_AVG_DQTY p_num = 30
        'volume_multiplier': 1.0,  # Oracle: l_avg_trade * 1 (修改自2为1)
    },
    
    # 突破确认参数
    'breakout_confirmation': {
        'consecutive_days': 3,     # Oracle: tp_i = 3
        'price_threshold': 1.03,   # Oracle: close_price > l_ypoint * 1.03
        'volume_threshold': 1.5,   # Oracle: jy_quantity > l_avg_qty * 1.5 (已注释)
    },
    
    # 上升趋势分析参数
    'uptrend_analysis': {
        'min_gap_days': 20,        # Oracle: (l_min_num1 + 20) <= l_upmax_num
    },
    
    # 数据库配置
    'database': {
        'temp_table_name': 'gp_jy_d_tmp',
        'result_table_name': 'gp_trend_result',
        'sequence_name': 'gp_trend_result_s',
    },
    
    # 邮件配置
    'email': {
        'default_subject': 'SZ_SH_TREND',
        'tactics_code': 'TACTICS_W_NEW',
        'branch_code': '8888',
    }
}

# 多线程配置
THREADING_CONFIG = {
    'max_workers': 4,              # 最大工作线程数
    'timeout': 1800,               # 任务超时时间（秒）
    'chunk_size': 50,              # 每批处理的股票数量
    'batch_insert_size': 1000,     # 批量插入的大小
}

# 数据过滤配置
DATA_FILTER_CONFIG = {
    'exchanges': ['sh', 'sz'],     # Oracle: where t.jys_no in ('sh', 'sz')
    'min_trading_days': 100,       # 最少交易天数
    'start_date': '2013-01-01',    # 分析开始日期
}

# 调试配置
DEBUG_CONFIG = {
    'enable_detailed_logging': False,  # 减少详细日志输出
    'save_intermediate_data': False,
    'test_single_stock': None,         # 设置为None以处理全量股票
    'print_debug_info': False,         # 减少调试信息输出
}

# 数学计算配置
MATH_CONFIG = {
    'natural_log_base': 2.71828183,  # Oracle: 2.71828183
    'precision': 8,                  # Oracle: round(..., 8)
}
