"""
趋势分析邮件配置文件
参考sw_py的邮件配置，适配trend模块
"""

EMAIL_CONFIG = {
    'smtp_server': 'smtp.exmail.qq.com',
    'smtp_port': 465,
    'use_ssl': True,
    
    # 邮箱配置（与sw_py保持一致）
    'username': '<EMAIL>',               # 发件人邮箱
    'password': 'cAkcpgvAtBgfnT3W',             # 邮箱授权码
    'from_email': '<EMAIL>',             # 发件人邮箱
    'to_emails': ["<EMAIL>", "<EMAIL>"],        # 收件人邮箱列表
    #, "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"
    'subject': '趋势线策略技术分析结果报告',
    'enable_email': True,                    # 设为真实发送邮件，False禁用邮件发送
}

# 邮件内容模板配置
EMAIL_TEMPLATE_CONFIG = {
    'subject_template': '{date} 趋势线策略分析结果 - {framework}框架',
    'body_template': """各位好！

附件是{date}的趋势线策略分析数据，请查看。

本次分析概况：
- 分析时间框架：{time_frames}
- 分析股票总数：{total_stocks}
- 发现趋势信号：{found_patterns}个
- 分析完成时间：{completion_time}

趋势线策略说明：
- 基于{time_frames}日时间框架进行趋势线分析
- 识别下降趋势线突破信号
- 验证交易量配合情况
- 确认上升趋势延续性

更多精彩敬请期待！

此邮件由趋势分析系统自动发送。
""",
    
    'csv_filename_template': 'trend_analysis_{date}_{framework}.csv',
    'excel_filename_template': 'trend_analysis_{date}_{framework}.xlsx',
}

# 使用说明：
# 1. 复制此文件为 email_config.py
# 2. 填写实际的邮箱信息
# 3. 获取邮箱授权码（不是登录密码）
#    - 163邮箱：登录网页版 -> 设置 -> POP3/SMTP/IMAP -> 开启SMTP服务 -> 获取授权码
#    - QQ邮箱：登录网页版 -> 设置 -> 账户 -> 开启SMTP服务 -> 获取授权码
# 4. 重新运行分析程序
