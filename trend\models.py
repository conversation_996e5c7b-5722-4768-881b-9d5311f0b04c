"""
趋势分析数据模型
对应Oracle gp_Trend_pkg的数据库操作
"""

import sys
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, date
import logging
import math
import pymysql

# 导入本地数据库配置
from config.database_config import MYSQL_CONFIG, CONNECTION_POOL_CONFIG


class DatabaseManager:
    """独立的数据库管理器，不依赖sw_py"""

    def __init__(self):
        self.connection = None
        self.logger = logging.getLogger(__name__)
        self.connect()

    def connect(self):
        """建立数据库连接"""
        try:
            self.connection = pymysql.connect(**MYSQL_CONFIG)
            # 减少日志输出：数据库连接成功不再记录
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise

    def execute_query(self, sql: str, params=None):
        """执行查询"""
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            self.logger.error(f"执行查询失败: {e}")
            raise

    def execute_update(self, sql: str, params=None):
        """执行更新"""
        try:
            with self.connection.cursor() as cursor:
                result = cursor.execute(sql, params)
                self.connection.commit()
                return result
        except Exception as e:
            self.logger.error(f"执行更新失败: {e}")
            self.connection.rollback()
            raise

    def close(self):
        """关闭连接"""
        if self.connection:
            self.connection.close()


class TrendDatabaseManager:
    """趋势分析数据库管理器"""

    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        """初始化数据库管理器"""
        self.logger = logging.getLogger(__name__)

        if db_manager:
            self.db = db_manager
        else:
            # 创建独立的数据库管理器
            try:
                self.db = DatabaseManager()
            except Exception as e:
                self.logger.warning(f"数据库连接失败，将使用模拟模式: {e}")
                self.db = None

    def execute_query(self, sql: str, params=None):
        """执行查询 - 为邮件发送提供查询接口"""
        if not self.db:
            # 模拟模式：返回模拟数据
            # 减少日志输出：模拟模式不再记录详细信息
            return [
                {
                    '股票代码': '601002', '时间框架': 200, 'A0下降趋势第一高点': 10, 'A2下降趋势第二高点': 8,
                    '系数A': 1.5, '系数B': 2.0, 'A0最高价': 15.0, 'A2最高价': 12.0, 'B0下降趋势确认': 'Y',
                    'A1下降趋势拐点': 5, 'B2上升趋势第二低点': 7, '系数C': 0.8, '系数D': 1.2,
                    'A1最低价': 8.0, 'B2最低价': 9.0, 'B1上升趋势最高点': '2025-07-01', '类型': '自然数', 'flag': 'Y'
                },
                {
                    '股票代码': '000001', '时间框架': 200, 'A0下降趋势第一高点': 12, 'A2下降趋势第二高点': 9,
                    '系数A': 1.8, '系数B': 2.2, 'A0最高价': 18.0, 'A2最高价': 14.0, 'B0下降趋势确认': 'Y',
                    'A1下降趋势拐点': 6, 'B2上升趋势第二低点': 8, '系数C': 0.9, '系数D': 1.3,
                    'A1最低价': 10.0, 'B2最低价': 11.0, 'B1上升趋势最高点': '2025-07-02', '类型': '自然对数', 'flag': 'N'
                }
            ]
        return self.db.execute_query(sql, params)

    def create_trend_result_table(self):
        """创建趋势分析结果表 - 对应Oracle GP_TREND_RESULT"""
        if not self.db:
            # 减少日志输出：模拟模式不再记录
            return

        sql = """
        CREATE TABLE IF NOT EXISTS gp_trend_result (
            record_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '序号',
            gp_num VARCHAR(10) NOT NULL COMMENT '股票代码',
            dates_type INT NOT NULL COMMENT '时间框架(200天/400天/600天)',
            down_a0 DATE COMMENT 'A0日期(下降点1)',
            down_a2 DATE COMMENT 'A2日期(下降点2)',
            point_a DECIMAL(15,8) COMMENT '系数：A',
            point_b DECIMAL(15,8) COMMENT '系数：B',
            top_a0 DECIMAL(10,4) COMMENT 'A0最高价',
            top_a2 DECIMAL(10,4) COMMENT 'A2最高价',
            tp_b0 DATE COMMENT 'B0日期(突破点)',
            up_a1 DATE COMMENT 'A1日期(上升点1)',
            up_b2 DATE COMMENT 'B2日期(上升点2)',
            point_c DECIMAL(15,8) COMMENT '系数：C',
            point_d DECIMAL(15,8) COMMENT '系数：D',
            low_a1 DECIMAL(10,4) COMMENT 'A1最低价',
            low_b2 DECIMAL(10,4) COMMENT 'B2最低价',
            date_b1 DATE COMMENT 'B1日期',
            attribute1 VARCHAR(100) COMMENT '类型(自然数/自然对数)',
            attribute2 VARCHAR(100) COMMENT '标志',
            attribute3 VARCHAR(100),
            attribute4 VARCHAR(100),
            attribute5 VARCHAR(100),
            attribute6 VARCHAR(100),
            attribute7 VARCHAR(100),
            attribute8 VARCHAR(100),
            attribute9 VARCHAR(100),
            attribute10 VARCHAR(100),
            attribute11 VARCHAR(100),
            attribute12 VARCHAR(100),
            attribute13 VARCHAR(100),
            attribute14 VARCHAR(100),
            attribute15 VARCHAR(100),
            creation_date DATETIME DEFAULT CURRENT_TIMESTAMP,

            UNIQUE KEY uk_gp_dates_attr (gp_num, dates_type, attribute1),
            KEY idx_gp_num (gp_num),
            KEY idx_dates_type (dates_type),
            KEY idx_creation_date (creation_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='趋势分析结果表'
        """

        self.db.execute_update(sql)
        # 减少日志输出：表创建完成不再记录
    
    def create_temp_table(self):
        """创建临时表 - 对应Oracle GP_JY_D_TMP"""
        if not self.db:
            # 减少日志输出：模拟模式不再记录
            return

        sql = """
        CREATE TABLE IF NOT EXISTS gp_jy_d_tmp (
            row_num INT NOT NULL,
            record_id BIGINT,
            gp_num VARCHAR(10),
            jy_date DATE,
            open_price DECIMAL(10,4),
            high_price DECIMAL(10,4),
            close_price DECIMAL(10,4),
            low_price DECIMAL(10,4),
            jy_quantity BIGINT,
            jy_amount DECIMAL(15,4),
            adj_close DECIMAL(10,4),
            creation_date DATETIME,

            PRIMARY KEY (row_num),
            KEY idx_gp_num (gp_num),
            KEY idx_jy_date (jy_date),
            KEY idx_high_price (high_price),
            KEY idx_close_price (close_price)
        ) ENGINE=MEMORY
        """

        self.db.execute_update(sql)
        # 减少日志输出：临时表创建完成不再记录
    
    def truncate_temp_table(self):
        """清空临时表 - 对应Oracle TRUNCATE TABLE GP_JY_D_TMP"""
        if not self.db:
            # 减少日志输出：模拟模式不再记录
            return
        sql = "TRUNCATE TABLE gp_jy_d_tmp"
        self.db.execute_update(sql)
    
    def load_stock_data_to_temp(self, stock_code: str):
        """加载股票数据到临时表 - 对应Oracle插入GP_JY_D_TMP的逻辑"""
        if not self.db:
            # 减少日志输出：模拟模式不再记录
            return 0

        # 先清空临时表
        self.truncate_temp_table()

        # 从stock_zh_a_hist_hfq表加载数据，按日期排序并添加行号
        # 使用与sw_py一致的数据源表
        sql = """
        INSERT INTO gp_jy_d_tmp
        (row_num, record_id, gp_num, jy_date, open_price, high_price,
         close_price, low_price, jy_quantity, jy_amount, adj_close, creation_date)
        SELECT
            ROW_NUMBER() OVER (ORDER BY 日期) as row_num,
            ROW_NUMBER() OVER (ORDER BY 日期) as record_id,
            symbol as gp_num,
            日期 as jy_date,
            开盘 as open_price,
            最高 as high_price,
            收盘 as close_price,
            最低 as low_price,
            成交量 as jy_quantity,
            成交额 as jy_amount,
            收盘 as adj_close,
            NOW() as creation_date
        FROM stock_zh_a_hist_hfq
        WHERE symbol = %s AND 日期 >= '2013-01-01'
        ORDER BY 日期
        """

        rows_inserted = self.db.execute_update(sql, (stock_code,))
        return rows_inserted
    
    def get_max_row_num(self) -> int:
        """获取临时表最大行号 - 对应Oracle select max(row_num)"""
        if not self.db:
            return 500  # 模拟模式：返回足够的行数用于测试
        sql = "SELECT MAX(row_num) as max_num FROM gp_jy_d_tmp"
        result = self.db.execute_query(sql)
        return result[0]['max_num'] if result and result[0]['max_num'] else 0
    
    def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表 - 对应Oracle查询gp_info_t"""
        if not self.db:
            # 模拟模式：返回测试股票列表
            return [
                {'gp_num': '601002'},
                {'gp_num': '000001'},
                {'gp_num': '600036'},
                {'gp_num': '000002'},
                {'gp_num': '600519'}
            ]

        # 从stock_info_a_code_name表获取股票列表，并确保在stock_zh_a_hist_hfq表中有数据
        # 使用COLLATE解决字符集冲突问题
        sql = """
        SELECT DISTINCT s.code as gp_num
        FROM stock_info_a_code_name s
        WHERE s.code REGEXP '^[0-9]{6}$'
        AND EXISTS (
            SELECT 1 FROM stock_zh_a_hist_hfq h
            WHERE h.symbol COLLATE utf8mb4_unicode_ci = s.code COLLATE utf8mb4_unicode_ci
            AND h.`日期` >= '2013-01-01'
        )
        ORDER BY s.code
        """

        return self.db.execute_query(sql)
    
    def truncate_result_table(self):
        """清空结果表 - 对应Oracle truncate table gp_trend_result"""
        if not self.db:
            # 减少日志输出：模拟模式不再记录
            return
        sql = "TRUNCATE TABLE gp_trend_result"
        self.db.execute_update(sql)
        # 减少日志输出：结果表清空不再记录
    
    def insert_trend_result(self, result_data: Dict[str, Any]) -> bool:
        """插入趋势分析结果 - 对应Oracle insert into gp_trend_result"""
        if not self.db:
            # 减少日志输出：模拟模式不再记录详细信息
            return True

        sql = """
        INSERT INTO gp_trend_result
        (gp_num, dates_type, down_a0, down_a2, point_a, point_b, top_a0, top_a2,
         tp_b0, up_a1, up_b2, point_c, point_d, low_a1, low_b2, date_b1,
         attribute1, attribute2)
        VALUES
        (%(gp_num)s, %(dates_type)s, %(down_a0)s, %(down_a2)s, %(point_a)s, %(point_b)s,
         %(top_a0)s, %(top_a2)s, %(tp_b0)s, %(up_a1)s, %(up_b2)s, %(point_c)s,
         %(point_d)s, %(low_a1)s, %(low_b2)s, %(date_b1)s, %(attribute1)s, %(attribute2)s)
        """

        try:
            self.db.execute_update(sql, result_data)
            return True
        except Exception as e:
            self.logger.error(f"插入趋势分析结果失败: {e}")
            return False
    
    def calculate_avg_volume(self, stock_code: str, trade_date: date, days: int) -> float:
        """计算平均交易量 - 对应Oracle CALCUL_AVG_DQTY函数"""
        if not self.db:
            # 模拟模式：返回模拟的平均交易量
            return 1000000.0  # 100万股的模拟交易量

        # 直接从原始数据表查询，避免临时表重复引用问题
        try:
            # 直接从stock_zh_a_hist_hfq表计算平均交易量
            sql = """
            SELECT AVG(COALESCE(`成交量`, 0)) as avg_qty
            FROM stock_zh_a_hist_hfq
            WHERE symbol = %s
            AND `日期` < %s
            AND `日期` >= DATE_SUB(%s, INTERVAL %s DAY)
            """

            result = self.db.execute_query(sql, (stock_code, trade_date, trade_date, days))

            if result and result[0]['avg_qty'] is not None:
                return float(result[0]['avg_qty'])
            else:
                return 99999999999999.0  # Oracle默认值

        except Exception as e:
            self.logger.error(f"计算平均交易量失败: {e}")
            return 99999999999999.0  # Oracle异常处理返回值
