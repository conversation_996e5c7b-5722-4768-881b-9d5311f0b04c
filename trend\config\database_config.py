"""
趋势分析数据库配置
独立的数据库配置，不依赖sw_py
"""

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'root',
    'password': 'KingSt@r0308',
    'database': 'stock_data',
    'charset': 'utf8mb4',
    'autocommit': True,
    'connect_timeout': 60,
    'read_timeout': 60,
    'write_timeout': 60,
}

# 连接池配置
CONNECTION_POOL_CONFIG = {
    'pool_name': 'trend_analysis_pool',
    'pool_size': 5,
    'pool_reset_session': True,
}
